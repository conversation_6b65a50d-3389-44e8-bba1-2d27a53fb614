# Dashboard Tender Integration

This document explains how the `useTenders` hook has been integrated into the dashboard component to provide real-time tender data with search and filtering capabilities.

## Integration Overview

The dashboard now uses the `useTenders` hook to fetch and display real tender data in the "Recent Tenders" tab, replacing the previous mock data implementation.

## Key Features Implemented

### 1. **Real-Time Data Fetching**
- Uses SWR for automatic caching and revalidation
- Displays actual tender data from the API
- Automatic refresh on focus and reconnection

### 2. **Advanced Search & Filtering**
- **Text Search**: Search across OCID, description, and other tender fields
- **Status Filtering**: Filter by tender status (All, Active, Completed, Pending)
- **Category Filtering**: Filter by tender category (All, Construction, Services)
- **Reset Filters**: One-click reset of all filters

### 3. **Pagination Support**
- Server-side pagination with configurable page size (10 items per page)
- Previous/Next navigation buttons
- Page count and total items display
- Automatic loading states during page changes

### 4. **Enhanced Table Display**
- **OCID Column**: Shows tender OCID with category badge
- **Description Column**: Displays description with procuring entity
- **Status Column**: Color-coded status badges with icons
- **Created Date Column**: Formatted creation date with clock icon

### 5. **Error Handling & Loading States**
- Loading spinners during data fetching
- Error states with retry functionality
- Empty states for no data scenarios
- Graceful handling of network failures

## Component Structure

### State Management
```typescript
// Tender-specific state
const [tenderSearchTerm, setTenderSearchTerm] = useState("");
const [tenderStatusFilter, setTenderStatusFilter] = useState("all");
const [tenderCategoryFilter, setTenderCategoryFilter] = useState("all");
const [currentPage, setCurrentPage] = useState(1);

// Hook integration
const {
  tenders,
  pagination,
  isLoading: tendersLoading,
  error: tendersError,
  refreshTenders,
} = useTenders({
  page: currentPage,
  limit: 10,
  search: tenderSearchTerm.trim() || undefined,
  status: tenderStatusFilter !== "all" ? tenderStatusFilter : undefined,
  category: tenderCategoryFilter !== "all" ? tenderCategoryFilter : undefined,
  sortBy: "createdAt",
  sortOrder: "desc",
});
```

### Filter Controls
The dashboard includes comprehensive filter controls:

1. **Search Input**: Real-time text search
2. **Status Buttons**: Quick status filtering
3. **Category Buttons**: Category-based filtering
4. **Reset Button**: Clear all filters

### Table Columns
Custom table columns optimized for tender data:

1. **OCID**: Primary identifier with category badge
2. **Description**: Truncated description with entity info
3. **Status**: Color-coded status with appropriate icons
4. **Created**: Formatted date with visual indicator

## Usage Examples

### Basic Usage
The dashboard automatically loads and displays tenders when the "Recent Tenders" tab is selected.

### Filtering Tenders
Users can:
- Type in the search box to filter by text
- Click status buttons to filter by tender status
- Click category buttons to filter by category
- Use the reset button to clear all filters

### Pagination
Users can navigate through pages using:
- Previous/Next buttons
- Page indicator showing current page and total pages
- Total count display

## Performance Optimizations

### SWR Benefits
- **Caching**: Reduces API calls through intelligent caching
- **Background Updates**: Keeps data fresh without blocking UI
- **Deduplication**: Prevents duplicate requests
- **Error Retry**: Automatic retry on failures

### Query Optimization
- **Conditional Queries**: Only sends defined filter parameters
- **Debounced Search**: Prevents excessive API calls during typing
- **Server-Side Filtering**: Reduces data transfer and improves performance

## Error Handling

### Network Errors
- Displays user-friendly error messages
- Provides retry buttons for failed requests
- Maintains filter state during errors

### Empty States
- Different messages for no data vs. filtered results
- Clear visual indicators using appropriate icons
- Helpful text to guide user actions

## Integration Benefits

1. **Consistent UX**: Matches existing dashboard patterns
2. **Real Data**: Shows actual tender information
3. **Performance**: Optimized loading and caching
4. **Accessibility**: Proper loading states and error handling
5. **Scalability**: Handles large datasets with pagination

## Future Enhancements

Potential improvements that could be added:

1. **Advanced Filters**: Date range, amount filters, etc.
2. **Sorting Options**: Multiple column sorting
3. **Export Functionality**: CSV/PDF export of filtered results
4. **Bulk Actions**: Select and perform actions on multiple tenders
5. **Real-time Updates**: WebSocket integration for live updates

The integration provides a robust, user-friendly interface for managing and viewing tender data within the dashboard context.
