"use client";

import React, { useState, useMemo } from "react";
import { Listing } from "@/layouts/dashboard/details/basic";
import { Button } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/common/ui/tabs";
import {
  Clock,
  CheckCircle,
  AlertCircle,
  Activity,
  Package,
} from "lucide-react";
import { useTenders } from "@/hooks/useTenders";
import type { TenderResponse } from "@/store/actions/tender";

// Mock data for dashboard statistics
const dashboardStats = [
  {
    // icon: FileText,
    name: "Total Documents",
    value: 1247,
    valueType: "number" as const,
    caption: "+12% from last month",
    color: "blue" as const,
  },
  {
    // icon: Users,
    name: "Active Clients",
    value: 89,
    valueType: "number" as const,
    caption: "+5 new this week",
    color: "green" as const,
  },
  {
    // icon: DollarSign,
    name: "Revenue",
    value: 125000,
    valueType: "dollar" as const,
    caption: "+8% from last month",
    color: "primary" as const,
  },
  {
    // icon: TrendingUp,
    name: "Growth Rate",
    value: 23.5,
    valueType: "percent" as const,
    caption: "Quarterly growth",
    color: "amber" as const,
  },
];

// Mock data for recent activities
const recentActivities = [
  {
    id: "1",
    title: "New contract signed",
    description: "Contract #2024-001 with Acme Corp",
    timestamp: "2 hours ago",
    type: "contract",
    status: "completed",
  },
  {
    id: "2",
    title: "Document uploaded",
    description: "Financial report Q4 2024.pdf",
    timestamp: "4 hours ago",
    type: "document",
    status: "completed",
  },
  {
    id: "3",
    title: "Client meeting scheduled",
    description: "Review meeting with TechStart Inc",
    timestamp: "6 hours ago",
    type: "meeting",
    status: "pending",
  },
  {
    id: "4",
    title: "Proposal submitted",
    description: "Marketing campaign proposal for RetailCo",
    timestamp: "1 day ago",
    type: "proposal",
    status: "pending",
  },
  {
    id: "5",
    title: "Payment received",
    description: "$15,000 from GlobalTech Solutions",
    timestamp: "2 days ago",
    type: "payment",
    status: "completed",
  },
];

export function DashboardContainer() {
  // Filter state for activities (requests tab)
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Filter state for tenders
  const [tenderSearchTerm, setTenderSearchTerm] = useState("");
  const [tenderStatusFilter, setTenderStatusFilter] = useState("all");
  const [tenderCategoryFilter, setTenderCategoryFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);

  // Use tenders hook with query parameters
  const {
    tenders,
    pagination,
    isLoading: tendersLoading,
    error: tendersError,
    refreshTenders,
  } = useTenders({
    page: currentPage,
    limit: 10,
    search: tenderSearchTerm.trim() || undefined,
    status: tenderStatusFilter !== "all" ? tenderStatusFilter : undefined,
    category: tenderCategoryFilter !== "all" ? tenderCategoryFilter : undefined,
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  // Filter activities based on search term and status (for requests tab)
  const filteredActivities = useMemo(() => {
    let filtered = recentActivities;

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (activity) =>
          activity.title.toLowerCase().includes(searchLower) ||
          activity.description.toLowerCase().includes(searchLower) ||
          activity.type.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(
        (activity) => activity.status === statusFilter
      );
    }

    return filtered;
  }, [searchTerm, statusFilter]);

  // Handle refresh action for activities
  const handleRefresh = () => {
    console.log("Refreshing dashboard data...");
    // In a real app, this would refetch data
  };

  // Handle refresh action for tenders
  const handleTenderRefresh = () => {
    refreshTenders();
  };

  // Reset tender filters
  const resetTenderFilters = () => {
    setTenderSearchTerm("");
    setTenderStatusFilter("all");
    setTenderCategoryFilter("all");
    setCurrentPage(1);
  };

  // Tender table columns
  const tenderColumns = [
    {
      key: "ocid",
      label: "OCID",
      render: (tender: TenderResponse) => (
        <div className="flex flex-col">
          <p className="font-medium text-sm">{tender.ocid}</p>
          {tender.category && (
            <Badge variant="outline" className="w-fit mt-1 text-xs">
              {tender.category}
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: "description",
      label: "Description",
      render: (tender: TenderResponse) => (
        <div className="max-w-md">
          <p className="text-sm line-clamp-2">
            {tender.description || "No description available"}
          </p>
          {tender.procuring_entity && (
            <p className="text-xs text-muted-foreground mt-1">
              {tender.procuring_entity}
            </p>
          )}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (tender: TenderResponse) => (
        <Badge
          variant={
            tender.status === "active" || tender.status === "online"
              ? "default"
              : tender.status === "completed" || tender.status === "closed"
              ? "secondary"
              : "outline"
          }
          className="capitalize"
        >
          {tender.status === "active" || tender.status === "online" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : tender.status === "completed" || tender.status === "closed" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : (
            <AlertCircle className="h-3 w-3 mr-1" />
          )}
          {tender.status || "unknown"}
        </Badge>
      ),
    },
    {
      key: "createdAt",
      label: "Created",
      render: (tender: TenderResponse) => (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          {new Date(tender.createdAt).toLocaleDateString()}
        </div>
      ),
    },
  ];

  // Activity table columns (for requests tab)
  const activityColumns = [
    {
      key: "title",
      label: "Activity",
      render: (item: any) => (
        <div className="flex items-center gap-3">
          <p className="font-medium">{item.title}</p>
          <p className="text-sm text-muted-foreground">{item.description}</p>
        </div>
      ),
    },
    {
      key: "timestamp",
      label: "Time",
      render: (item: any) => (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          {item.timestamp}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (item: any) => (
        <Badge
          variant={item.status === "completed" ? "default" : "secondary"}
          className="capitalize"
        >
          {item.status === "completed" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : (
            <AlertCircle className="h-3 w-3 mr-1" />
          )}
          {item.status}
        </Badge>
      ),
    },
  ];

  return (
    <Listing className="p-6 space-y-6">
      {/* Dashboard Header */}
      <Listing.Header title={{ text: "Welcome back, Sienna", size: "2xl" }} />

      {/* Statistics Cards */}
      <Listing.Statistics columns="grid-cols-4">
        {dashboardStats.map((stat, index) => (
          <Listing.StatCard
            key={index}
            name={stat.name}
            value={stat.value}
            valueType={stat.valueType}
            caption={stat.caption}
            color={stat.color}
          />
        ))}
      </Listing.Statistics>

      {/* Tabs */}
      <Tabs defaultValue="tenders" className="w-full">
        <TabsList className="bg-muted border-border mb-6">
          <TabsTrigger
            value="tenders"
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          >
            Recent Tenders
          </TabsTrigger>
          <TabsTrigger
            value="requests"
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          >
            Recent Requests
          </TabsTrigger>
        </TabsList>

        {/* Recent Tenders */}
        <TabsContent value="tenders" className="mt-0">
          <Listing className="space-y-1">
            <Listing.Header
              title={{ text: "Recent Tenders", size: "md" }}
              caption="Latest tenders posted to the platform"
            />

            {/* Tender Filters */}
            <Listing.Filters
              searchTerm={tenderSearchTerm}
              onSearchChange={setTenderSearchTerm}
              onRefresh={handleTenderRefresh}
              loading={tendersLoading}
              customActions={
                <div className="flex items-center gap-2 flex-wrap">
                  {/* Status Filters */}
                  <div className="flex items-center gap-1">
                    <Button
                      variant={
                        tenderStatusFilter === "all" ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() => setTenderStatusFilter("all")}
                    >
                      All Status
                    </Button>
                    <Button
                      variant={
                        tenderStatusFilter === "active" ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() => setTenderStatusFilter("active")}
                    >
                      Active
                    </Button>
                    <Button
                      variant={
                        tenderStatusFilter === "completed"
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() => setTenderStatusFilter("completed")}
                    >
                      Completed
                    </Button>
                    <Button
                      variant={
                        tenderStatusFilter === "pending" ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() => setTenderStatusFilter("pending")}
                    >
                      Pending
                    </Button>
                  </div>

                  {/* Category Filters */}
                  <div className="flex items-center gap-1">
                    <Button
                      variant={
                        tenderCategoryFilter === "all" ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() => setTenderCategoryFilter("all")}
                    >
                      All Categories
                    </Button>
                    <Button
                      variant={
                        tenderCategoryFilter === "construction"
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() => setTenderCategoryFilter("construction")}
                    >
                      Construction
                    </Button>
                    <Button
                      variant={
                        tenderCategoryFilter === "services"
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() => setTenderCategoryFilter("services")}
                    >
                      Services
                    </Button>
                  </div>

                  {/* Reset Filters */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={resetTenderFilters}
                    className="text-muted-foreground"
                  >
                    Reset
                  </Button>
                </div>
              }
            />

            {/* Tenders Table */}
            <Listing.Table
              data={tenders}
              columns={tenderColumns}
              loading={tendersLoading}
              emptyState={
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {tendersError
                      ? "Failed to load tenders"
                      : tenderSearchTerm ||
                        tenderStatusFilter !== "all" ||
                        tenderCategoryFilter !== "all"
                      ? "No tenders match your filters"
                      : "No tenders available"}
                  </p>
                  {tendersError && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleTenderRefresh}
                      className="mt-2"
                    >
                      Try Again
                    </Button>
                  )}
                </div>
              }
            />

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
              <div className="flex justify-between items-center mt-4 pt-4 border-t">
                <span className="text-sm text-muted-foreground">
                  Showing {tenders.length} of {pagination.total} tenders
                </span>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1 || tendersLoading}
                  >
                    Previous
                  </Button>
                  <span className="px-3 py-1 text-sm">
                    Page {currentPage} of {pagination.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      setCurrentPage(
                        Math.min(pagination.totalPages, currentPage + 1)
                      )
                    }
                    disabled={
                      currentPage === pagination.totalPages || tendersLoading
                    }
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </Listing>
        </TabsContent>

        {/* Recent Requests */}
        <TabsContent value="requests" className="mt-0">
          <Listing className="space-y-1">
            <Listing.Header
              title={{ text: "Recent Requests", size: "md" }}
              caption="Latest do it for me requests, posted to the platform"
            />

            {/* Filters */}
            <Listing.Filters
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              onRefresh={handleRefresh}
              loading={false}
              customActions={
                <div className="flex items-center gap-2">
                  <Button
                    variant={statusFilter === "all" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setStatusFilter("all")}
                  >
                    All
                  </Button>
                  <Button
                    variant={
                      statusFilter === "completed" ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => setStatusFilter("completed")}
                  >
                    Completed
                  </Button>
                  <Button
                    variant={statusFilter === "pending" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setStatusFilter("pending")}
                  >
                    Pending
                  </Button>
                </div>
              }
            />

            {/* Main Content Grid */}
            <Listing.Table
              data={filteredActivities}
              columns={activityColumns}
              loading={false}
              emptyState={
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {searchTerm || statusFilter !== "all"
                      ? "No activities match your filters"
                      : "No recent activities"}
                  </p>
                </div>
              }
            />
          </Listing>
        </TabsContent>
      </Tabs>
    </Listing>
  );
}
