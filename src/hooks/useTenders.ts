"use client";

import { useCallback, useState } from "react";
import { useDispatch } from "react-redux";
import useS<PERSON> from "swr";
import { toast } from "sonner";
import type { AppDispatch } from "@/store";
import { fetcher } from "@/lib/common/requests";
import {
  createTender,
  updateTender,
  deleteTender,
  upsertTender,
  searchTenders,
  seedTenders,
  bulkUpsertTenders,
  type TenderCreateData,
  type TenderUpdateData,
  type TenderSearchQuery,
  type TenderResponse,
  type TendersListResponse,
} from "@/store/actions/tender";

// Hook for managing all tenders with SWR
export function useTenders(query?: Record<string, any>) {
  const dispatch = useDispatch<AppDispatch>();
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<TenderResponse[]>([]);

  // Build query string for SWR key
  const queryString = query
    ? `?${new URLSearchParams(
        Object.entries(query).reduce((acc, [key, value]) => {
          if (value !== undefined && value !== null && value !== "") {
            acc[key] = String(value);
          }
          return acc;
        }, {} as Record<string, string>)
      ).toString()}`
    : "";

  // SWR hook for fetching tenders list
  const {
    data: tendersData,
    error: tendersError,
    mutate: mutateTenders,
    isLoading: isLoadingTenders,
  } = useSWR<{
    success: boolean;
    error: boolean;
    message: string;
    data: TenderResponse[];
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }>(`tender${queryString}`, fetcher);

  // Extract data from SWR response
  const tenders = tendersData?.data || [];
  const pagination = tendersData?.pagination;
  const error = tendersError;
  const isLoading = isLoadingTenders;

  // Refresh tenders data
  const refreshTenders = useCallback(() => {
    mutateTenders();
  }, [mutateTenders]);

  // Create tender action
  const createNewTender = useCallback(
    async (tenderData: TenderCreateData) => {
      try {
        const result = await dispatch(createTender(tenderData));
        if (createTender.fulfilled.match(result)) {
          // Refresh SWR cache after successful creation
          mutateTenders();
          return result.payload;
        } else {
          throw new Error(result.payload?.message || "Failed to create tender");
        }
      } catch (error) {
        console.error("Failed to create tender:", error);
        throw error;
      }
    },
    [dispatch, mutateTenders]
  );

  // Update tender action
  const updateExistingTender = useCallback(
    async (tenderData: TenderUpdateData) => {
      try {
        const result = await dispatch(updateTender(tenderData));
        if (updateTender.fulfilled.match(result)) {
          // Refresh SWR cache after successful update
          mutateTenders();
          return result.payload;
        } else {
          throw new Error(result.payload?.message || "Failed to update tender");
        }
      } catch (error) {
        console.error("Failed to update tender:", error);
        throw error;
      }
    },
    [dispatch, mutateTenders]
  );

  // Delete tender action
  const deleteExistingTender = useCallback(
    async (id: string) => {
      try {
        const result = await dispatch(deleteTender(id));
        if (deleteTender.fulfilled.match(result)) {
          // Refresh SWR cache after successful deletion
          mutateTenders();
          return result.payload;
        } else {
          throw new Error(result.payload?.message || "Failed to delete tender");
        }
      } catch (error) {
        console.error("Failed to delete tender:", error);
        throw error;
      }
    },
    [dispatch, mutateTenders]
  );

  // Upsert tender action
  const upsertExistingTender = useCallback(
    async (tenderData: TenderCreateData) => {
      try {
        const result = await dispatch(upsertTender(tenderData));
        if (upsertTender.fulfilled.match(result)) {
          // Refresh SWR cache after successful upsert
          mutateTenders();
          return result.payload;
        } else {
          throw new Error(result.payload?.message || "Failed to save tender");
        }
      } catch (error) {
        console.error("Failed to upsert tender:", error);
        throw error;
      }
    },
    [dispatch, mutateTenders]
  );

  // Search tenders action
  const searchTendersFunction = useCallback(
    async (searchQuery: TenderSearchQuery) => {
      try {
        setIsSearching(true);
        const result = await dispatch(searchTenders(searchQuery));
        if (searchTenders.fulfilled.match(result)) {
          setSearchResults(result.payload);
          return result.payload;
        } else {
          throw new Error(
            result.payload?.message || "Failed to search tenders"
          );
        }
      } catch (error) {
        console.error("Failed to search tenders:", error);
        toast.error("Failed to search tenders");
        throw error;
      } finally {
        setIsSearching(false);
      }
    },
    [dispatch]
  );

  // Seed tenders action
  const seedTendersFunction = useCallback(async () => {
    try {
      const result = await dispatch(seedTenders());
      if (seedTenders.fulfilled.match(result)) {
        // Refresh SWR cache after successful seeding
        mutateTenders();
        return result.payload;
      } else {
        throw new Error(result.payload?.message || "Failed to seed tenders");
      }
    } catch (error) {
      console.error("Failed to seed tenders:", error);
      throw error;
    }
  }, [dispatch, mutateTenders]);

  // Bulk upsert tenders action
  const bulkUpsertTendersFunction = useCallback(
    async (tenders: TenderCreateData[]) => {
      try {
        const result = await dispatch(bulkUpsertTenders(tenders));
        if (bulkUpsertTenders.fulfilled.match(result)) {
          // Refresh SWR cache after successful bulk operation
          mutateTenders();
          return result.payload;
        } else {
          throw new Error(
            result.payload?.message || "Failed to perform bulk operation"
          );
        }
      } catch (error) {
        console.error("Failed to bulk upsert tenders:", error);
        throw error;
      }
    },
    [dispatch, mutateTenders]
  );

  // Clear search results
  const clearSearchResults = useCallback(() => {
    setSearchResults([]);
  }, []);

  return {
    // State (from SWR)
    tenders,
    pagination,
    searchResults,
    isLoading,
    isSearching,
    error,

    // Data refresh actions (SWR-based)
    refreshTenders,

    // CRUD actions (Redux-based with SWR refresh)
    createTender: createNewTender,
    updateTender: updateExistingTender,
    deleteTender: deleteExistingTender,
    upsertTender: upsertExistingTender,
    searchTenders: searchTendersFunction,
    seedTenders: seedTendersFunction,
    bulkUpsertTenders: bulkUpsertTendersFunction,

    // Utility actions
    clearSearchResults,
  };
}

// Hook for getting a specific tender by ID using SWR
export function useTender(tenderId: string | null) {
  const {
    data: tenderData,
    error,
    mutate,
    isLoading,
  } = useSWR<{
    success: boolean;
    error: boolean;
    message: string;
    data: TenderResponse;
  }>(tenderId ? `tender/${tenderId}` : null, fetcher);

  return {
    tender: tenderData?.data,
    error,
    isLoading,
    refresh: mutate,
  };
}

// Hook for getting a tender by OCID using SWR
export function useTenderByOcid(ocid: string | null) {
  const {
    data: tenderData,
    error,
    mutate,
    isLoading,
  } = useSWR<{
    success: boolean;
    error: boolean;
    message: string;
    data: TenderResponse;
  }>(ocid ? `tender/ocid/${ocid}` : null, fetcher);

  return {
    tender: tenderData?.data,
    error,
    isLoading,
    refresh: mutate,
  };
}
