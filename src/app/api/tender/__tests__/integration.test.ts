/**
 * Integration tests for Tender API endpoints
 * 
 * These tests verify that the API routes properly interface with the NestTenderService
 * and handle requests/responses correctly.
 */

import { NextRequest } from 'next/server';
import { GET as getTenders, POST as createTender, PUT as upsertTender } from '../route';
import { GET as getTenderById, PUT as updateTender, DELETE as deleteTender } from '../[id]/route';
import { GET as getTenderByOcid } from '../ocid/[ocid]/route';
import { GET as searchTenders } from '../search/route';

// Mock the auth function
jest.mock('@/lib/auth', () => ({
  auth: jest.fn(() => Promise.resolve({
    user: { id: 'test-user-id', email: '<EMAIL>' }
  }))
}));

// Mock the NestTenderService
jest.mock('@/lib/api/services/tenders/nest', () => ({
  NestTenderService: jest.fn().mockImplementation(() => ({
    setContext: jest.fn(),
    getTenders: jest.fn().mockResolvedValue({
      success: true,
      data: [
        {
          id: 'test-tender-1',
          ocid: 'test-ocid-1',
          description: 'Test tender description',
          status: 'active'
        }
      ],
      pagination: {
        page: 1,
        limit: 20,
        total: 1,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false
      }
    }),
    getTenderById: jest.fn().mockResolvedValue({
      success: true,
      data: {
        id: 'test-tender-1',
        ocid: 'test-ocid-1',
        description: 'Test tender description',
        status: 'active'
      }
    }),
    getTenderByOcid: jest.fn().mockResolvedValue({
      success: true,
      data: {
        id: 'test-tender-1',
        ocid: 'test-ocid-1',
        description: 'Test tender description',
        status: 'active'
      }
    }),
    createTender: jest.fn().mockResolvedValue({
      success: true,
      data: {
        id: 'new-tender-id',
        ocid: 'new-ocid',
        description: 'New tender description',
        status: 'created'
      }
    }),
    updateTender: jest.fn().mockResolvedValue({
      success: true,
      data: {
        id: 'test-tender-1',
        ocid: 'test-ocid-1',
        description: 'Updated tender description',
        status: 'active'
      }
    }),
    upsertTender: jest.fn().mockResolvedValue({
      success: true,
      data: {
        id: 'upserted-tender-id',
        ocid: 'upserted-ocid',
        description: 'Upserted tender description',
        status: 'active'
      }
    }),
    deleteTender: jest.fn().mockResolvedValue({
      success: true,
      data: { id: 'test-tender-1', deleted: true }
    })
  }))
}));

describe('Tender API Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/tender', () => {
    it('should return list of tenders', async () => {
      const request = new NextRequest('http://localhost:3000/api/tender');
      const response = await getTenders(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
      expect(data.pagination).toBeDefined();
    });

    it('should handle query parameters', async () => {
      const request = new NextRequest('http://localhost:3000/api/tender?page=2&limit=10&status=active');
      const response = await getTenders(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });
  });

  describe('POST /api/tender', () => {
    it('should create a new tender', async () => {
      const tenderData = {
        ocid: 'new-ocid',
        description: 'New tender description',
        status: 'created'
      };

      const request = new NextRequest('http://localhost:3000/api/tender', {
        method: 'POST',
        body: JSON.stringify(tenderData),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await createTender(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data.ocid).toBe('new-ocid');
    });
  });

  describe('PUT /api/tender', () => {
    it('should upsert a tender', async () => {
      const tenderData = {
        ocid: 'upserted-ocid',
        description: 'Upserted tender description',
        status: 'active'
      };

      const request = new NextRequest('http://localhost:3000/api/tender', {
        method: 'PUT',
        body: JSON.stringify(tenderData),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await upsertTender(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.ocid).toBe('upserted-ocid');
    });
  });

  describe('GET /api/tender/[id]', () => {
    it('should return tender by ID', async () => {
      const request = new NextRequest('http://localhost:3000/api/tender/test-tender-1');
      const params = Promise.resolve({ id: 'test-tender-1' });
      
      const response = await getTenderById(request, { params });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.id).toBe('test-tender-1');
    });

    it('should return 400 for missing ID', async () => {
      const request = new NextRequest('http://localhost:3000/api/tender/');
      const params = Promise.resolve({ id: '' });
      
      const response = await getTenderById(request, { params });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.message).toContain('required');
    });
  });

  describe('GET /api/tender/ocid/[ocid]', () => {
    it('should return tender by OCID', async () => {
      const request = new NextRequest('http://localhost:3000/api/tender/ocid/test-ocid-1');
      const params = Promise.resolve({ ocid: 'test-ocid-1' });
      
      const response = await getTenderByOcid(request, { params });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.ocid).toBe('test-ocid-1');
    });
  });

  describe('GET /api/tender/search', () => {
    it('should search tenders', async () => {
      const request = new NextRequest('http://localhost:3000/api/tender/search?query=test&status=active');
      const response = await searchTenders(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
    });
  });
});
