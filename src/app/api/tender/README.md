# Tender API Documentation

This API provides comprehensive CRUD operations for managing tender data, with integration to the NestTenderService for database operations and external API seeding.

## Base URL
```
/api/tender
```

## Authentication
All endpoints support optional authentication. When authenticated, the user context is passed to the service layer for potential access control and logging.

## Endpoints

### 1. Get All Tenders
**GET** `/api/tender`

Retrieve all tenders with optional filtering, pagination, and search capabilities.

#### Query Parameters
- `id` (string, optional): Filter by specific tender ID
- `ocid` (string, optional): Filter by OCID (partial match)
- `language` (string, optional): Filter by language
- `initiation_type` (string, optional): Filter by initiation type
- `procuring_entity` (string, optional): Filter by procuring entity (partial match)
- `procuring_method` (string, optional): Filter by procurement method
- `category` (string, optional): Filter by category (partial match)
- `status` (string, optional): Filter by status (online, offline, active, inactive, etc.)
- `search` (string, optional): Search across multiple fields (ocid, description, procuring_entity, category)
- `page` (number, optional): Page number for pagination (default: 1)
- `limit` (number, optional): Number of items per page (default: 20, max: 100)
- `sortBy` (string, optional): Sort field (createdAt, updatedAt, ocid, description)
- `sortOrder` (string, optional): Sort order (asc, desc)
- `autoSeed` (boolean, optional): Enable/disable automatic data seeding (default: true)

#### Example Request
```bash
GET /api/tender?status=active&page=1&limit=10&search=construction
```

#### Response
```json
{
  "success": true,
  "data": [
    {
      "id": "tender-id-1",
      "ocid": "ocds-tender-001",
      "language": "en",
      "description": "Construction project tender",
      "status": "active",
      "procuring_entity": "City Council",
      "category": "Construction",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false
  }
}
```

### 2. Create Tender
**POST** `/api/tender`

Create a new tender.

#### Request Body
```json
{
  "ocid": "ocds-tender-002",
  "language": "en",
  "description": "New tender description",
  "status": "created",
  "procuring_entity": "Government Agency",
  "procuring_method": "open",
  "category": "Services"
}
```

#### Response
```json
{
  "success": true,
  "data": {
    "id": "new-tender-id",
    "ocid": "ocds-tender-002",
    "language": "en",
    "description": "New tender description",
    "status": "created",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

### 3. Upsert Tender
**PUT** `/api/tender`

Create or update a tender based on OCID.

#### Request Body
Same as POST request.

### 4. Get Tender by ID
**GET** `/api/tender/{id}`

Retrieve a specific tender by its ID.

#### Query Parameters
- `autoSeed` (boolean, optional): Enable/disable automatic data seeding if tender not found (default: true)

#### Example Request
```bash
GET /api/tender/tender-id-1?autoSeed=false
```

### 5. Update Tender
**PUT** `/api/tender/{id}`

Update a specific tender by its ID.

#### Request Body
```json
{
  "description": "Updated tender description",
  "status": "active"
}
```

### 6. Delete Tender
**DELETE** `/api/tender/{id}`

Delete a specific tender by its ID.

### 7. Get Tender by OCID
**GET** `/api/tender/ocid/{ocid}`

Retrieve a specific tender by its OCID.

#### Example Request
```bash
GET /api/tender/ocid/ocds-tender-001
```

### 8. Search Tenders
**GET** `/api/tender/search`

Advanced search for tenders with the same query parameters as the main GET endpoint, but with `autoSeed` defaulting to false for better search performance.

#### Example Request
```bash
GET /api/tender/search?query=construction&status=active&limit=5
```

### 9. Manual Seeding
**POST** `/api/tender/seed`

Manually trigger seeding of tender data from external API.

#### Request Body (Optional)
```json
{
  "skipCronCheck": false,
  "useBulkSeeding": true,
  "fallbackToSingleRecords": true,
  "batchSize": 50,
  "maxRetries": 3
}
```

**GET** `/api/tender/seed`

Get information about the seeding process.

### 10. Bulk Operations
**POST** `/api/tender/bulk`

Bulk upsert multiple tenders.

#### Request Body
```json
{
  "tenders": [
    {
      "ocid": "ocds-tender-003",
      "description": "Bulk tender 1",
      "status": "active"
    },
    {
      "ocid": "ocds-tender-004",
      "description": "Bulk tender 2",
      "status": "active"
    }
  ],
  "batchSize": 25
}
```

**GET** `/api/tender/bulk`

Get information about bulk operations.

## Error Responses

All endpoints return standardized error responses:

```json
{
  "success": false,
  "error": true,
  "message": "Error description",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `404`: Not Found
- `405`: Method Not Allowed
- `500`: Internal Server Error

## Data Schema

### Tender Object
```typescript
{
  id: string;
  ocid: string;
  language?: string;
  initiation_type?: string;
  description?: string;
  address?: any; // JSON field
  timeline?: any; // JSON field
  procuring_entity?: string;
  procuring_method?: string;
  items?: any[]; // JSON array
  parties?: any[]; // JSON array
  category?: string;
  status?: "online" | "offline" | "active" | "inactive" | "submitted" | 
           "received" | "negotiating" | "agreed" | "created" | "inprogress" | 
           "reviewing" | "completed" | "closed" | "terminated" | "pending";
  createdAt: string;
  updatedAt: string;
}
```

## Integration Notes

- The API automatically interfaces with the `NestTenderService` for all database operations
- Authentication context is passed to the service layer when available
- Automatic seeding from external APIs is supported and can be controlled via query parameters
- All responses follow the standardized `ServiceResponse` format
- Input validation is handled by Zod schemas in the service layer
- Comprehensive error handling and logging is built-in
